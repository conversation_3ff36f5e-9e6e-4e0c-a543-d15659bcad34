<?php

use Umimeweby\UWTheme\Service\Calculator\Year_Calculator;
use Umimeweby\UWTheme\Settings\UW_Settings_Page;

$uw_settings_group = rwmb_get_value(UW_Settings_Page::PREFIX . UW_Settings_Page::FIELD_GROUP_HEADER_CONTENT_CAROUSEL, ['object_type' => 'setting'], UW_Settings_Page::OPTION_MAIN_SETTINGS);
$uw_settings_title = UW_Settings_Page::PREFIX . UW_Settings_Page::FIELD_HEADER_CONTENT_TITLE;
$uw_settings_subtitle = UW_Settings_Page::PREFIX . UW_Settings_Page::FIELD_HEADER_CONTENT_SUBTITLE;
$uw_settings_description = UW_Settings_Page::PREFIX . UW_Settings_Page::FIELD_HEADER_CONTENT_DESCRIPTION;
$uw_settings_btn_text = UW_Settings_Page::PREFIX . UW_Settings_Page::FIELD_HEADER_CONTENT_BTN_TEXT;
$uw_settings_btn_link = UW_Settings_Page::PREFIX . UW_Settings_Page::FIELD_HEADER_CONTENT_BTN_LINK;
$uw_settings_image = UW_Settings_Page::PREFIX . UW_Settings_Page::FIELD_HEADER_CONTENT_CAROUSEL_IMAGE;
$uw_settings_under_carousel_title = rwmb_get_value(UW_Settings_Page::PREFIX . UW_Settings_Page::FIELD_HEADER_CONTENT_UNDER_CAROUSEL_TITLE, ['object_type' => 'setting'], UW_Settings_Page::OPTION_MAIN_SETTINGS);
$uw_settings_under_carousel_description = rwmb_get_value(UW_Settings_Page::PREFIX . UW_Settings_Page::FIELD_HEADER_CONTENT_UNDER_CAROUSEL_DESCRIPTION, ['object_type' => 'setting'], UW_Settings_Page::OPTION_MAIN_SETTINGS);
$uw_settings_under_carousel_btn_text = rwmb_get_value(UW_Settings_Page::PREFIX . UW_Settings_Page::FIELD_HEADER_CONTENT_UNDER_CAROUSEL_BTN_TEXT, ['object_type' => 'setting'], UW_Settings_Page::OPTION_MAIN_SETTINGS);
$uw_settings_under_carousel_btn_link = rwmb_get_value(UW_Settings_Page::PREFIX . UW_Settings_Page::FIELD_HEADER_CONTENT_UNDER_CAROUSEL_BTN_LINK, ['object_type' => 'setting'], UW_Settings_Page::OPTION_MAIN_SETTINGS);

$calculator = new Year_Calculator();
$years =  $calculator->get_years_since_start();

?>
<!-- ::::::::::::homepage carousel section:::::::::::: -->
<section class="relative z-40 mx-auto mb-16 w-full max-w-[1480px] lg:px-5">
    <div class="mx-auto w-full max-w-[1440px]">
        <div class="w-full pb-8 lg:rounded-[50px] lg:border bg-theme-purple/10 lg:p-7 lg:pb-7 lg:backdrop-blur-[15px]">
            <div class="mx-auto h-full max-w-[1170px] text-center lg:text-left">
                <div class="h-full swiper overflow-visible hero-slider lg:-mx-20 lg:px-20">
                    <div class="items-end swiper-wrapper lg:items-start">
                        <?php foreach ($uw_settings_group as $carousel) : ?>
                            <?php
                            $uw_settings_btn_link_value = !empty($carousel[$uw_settings_btn_link]) ? $carousel[$uw_settings_btn_link] : '';
                            $uw_settings_btn_link_url = get_permalink($uw_settings_btn_link_value);
                            ?>
                            <div class="h-full swiper-slide hero-slide">
                                <div class="flex flex-col items-center h-full lg:flex-row">
                                    <div class="w-full max-w-[535px]">
                                        <div class="w-full max-w-[500px]">
                                            <p class="mb-2.5 text-sm font-bold text-theme-purple md:text-xl md:leading-8">
                                                <?= !empty($carousel[$uw_settings_subtitle]) ? $carousel[$uw_settings_subtitle] : ''; ?>
                                            </p>
                                            <h3 class="heading-tertiary lg:heading-secondary mb-4 text-theme-purple lg:text-theme-purple">
                                                <?= !empty($carousel[$uw_settings_title]) ? $carousel[$uw_settings_title] : ''; ?>
                                            </h3>
                                            <p class="mt-4 mb-6 text-sm md:mt-7.5 md:mb-[50px] lg:text-base lg:leading-6 text-gray-700">
                                                <?= !empty($carousel[$uw_settings_description]) ? $carousel[$uw_settings_description] : ''; ?>
                                            </p>
                                            <?php if (!empty($uw_settings_btn_link_value)) : ?>
                                                <a href="<?= trailingslashit(esc_url($uw_settings_btn_link_url)) ?>" class="mx-auto btn-primary lg:mx-0">
                                                    <?= !empty($carousel[$uw_settings_btn_text]) ? $carousel[$uw_settings_btn_text] : ''; ?>
                                                    <svg xmlns="http://www.w3.org/2000/svg" width="31" height="30" fill="none">
                                                        <path fill="#FEFEFE" fill-rule="evenodd" d="M.624 9.904c-.832.712-.832 1.864 0 2.576 3.674 3.137 7.36 6.262 11.035 9.4.834.712 2.185.712 3.018 0 1.13-.965 2.258-1.931 3.387-2.897-3.054-2.593-6.082-5.238-9.155-7.8C11.932 8.582 14.969 6 18 3.412L14.61.532c-.832-.71-2.185-.71-3.017 0C7.935 3.655 4.283 6.783.624 9.904Z" clip-rule="evenodd" />
                                                        <path fill="#FEFEFE" fill-rule="evenodd" d="M12.697 11.016c3.055 2.595 6.088 5.224 9.155 7.801l-9.093 7.77c1.13.96 2.265 1.92 3.392 2.88.832.71 2.184.71 3.017 0 3.657-3.122 7.31-6.25 10.968-9.371.833-.712.833-1.864 0-2.576-3.674-3.138-7.36-6.263-11.034-9.4-.834-.712-2.185-.712-3.019 0l-3.386 2.896Z" clip-rule="evenodd" />
                                                    </svg>
                                                </a>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                    <div class="h-full w-full max-w-[620px]">
                                        <?php if ($carousel[$uw_settings_image]) : ?>
                                            <?php
                                            $images = $carousel[$uw_settings_image];
                                            $image = reset($images);
                                            ?>
                                            <img src="<?= wp_get_attachment_image_url($image, 'large') ?>" alt="<?= esc_attr($carousel[$uw_settings_title] ?? 'carousel image') ?>" class="h-auto w-auto translate-y-2.5 object-contain lg:translate-y-0" loading="lazy" decoding="async" />
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>

            <!-- Slider Dots -->
            <div class="relative z-50 mx-auto flex w-full max-w-[1170px] translate-y-4 items-center justify-center lg:translate-y-0">
                <div class="hero-dots flex items-center justify-center gap-3.5"></div>
            </div>
        </div>
    </div>
    
    <!-- Under carousel content deleted-->

</section>
