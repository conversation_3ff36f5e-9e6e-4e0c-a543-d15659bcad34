@layer components {

    /*-- ::::::::::::headings:::::::::::: */
    .heading-primary {
        @apply font-bold text-white text-50 leading-50;
    }

    .heading-secondary {
        @apply font-bold text-white text-40 leading-54;
    }

    .heading-tertiary {
        @apply text-2xl font-bold leading-8;
    }
    
    .nav-link {
        @apply relative text-sm font-bold text-white before:invisible before:absolute before:left-0 before:right-0 before:top-full before:block before:h-0.5 before:w-0 before:origin-left before:bg-white before:opacity-0 before:transition-all before:duration-300 before:content-[''] hover:before:visible hover:before:w-full before:hover:opacity-100 md:leading-7 lg:text-xl;
    }

    .nav-menu {
        @apply invisible absolute top-full right-0 z-[100] flex max-h-0 w-full min-w-[140px] max-w-[140px] origin-top scale-y-0 flex-col justify-between gap-4 rounded-[20px] rounded-tr-none bg-[#8078a0] py-[17px] px-[20px] opacity-0 transition-all duration-300 md:visible md:static md:mt-2.5 md:max-h-max md:min-w-full md:scale-y-100 md:flex-row md:items-center md:gap-2 md:bg-transparent md:py-0 md:px-0 md:opacity-100;
    }

    .nav-menu li a {
        @apply nav-link flex w-fit items-center gap-1.5 lg:gap-2.5 uppercase;
    }

    /*-- ::::::::::::footer:::::::::::: */
    .footer-services li a {
        @apply inline-block text-sm leading-loose transition-all text-light-purple hover:text-white hover:underline sm:text-base;
    }

    .footer-about li a {
        @apply inline-block text-base transition-all text-light-purple hover:text-white hover:underline;
    }

    .footer-about li {
        @apply md:py-1;
    }

    /*-- ::::::::::::custom css:::::::::::: */
    .dropShadow {
        filter: drop-shadow(0px 0px 30px #4c03ef);
    }

    .curve-clip::before {
        clip-path: polygon(100% 0, 0% 100%, 100% 100%);
    }

    .btn-primary {
        @apply flex w-fit items-center justify-center gap-2.5 rounded-[10px] bg-theme-pink py-2.5 px-7.5 text-sm font-bold leading-none text-white shadow-btn transition-all duration-300 hover:-translate-y-[2px] active:translate-y-[1px] md:rounded-[20px] md:text-xl md:leading-[50px];
    }

    .slider-dot {
        @apply block h-2.5 w-2.5 cursor-pointer rounded-full bg-light-purple transition-all duration-200 md:h-4 md:w-4;
    }

    .slider-dot.slider-dot-light {
        @apply bg-[#EAEAEA];
    }

    .slider-dot.slider-dot-active {
        @apply w-8 bg-theme-pink md:w-11;
    }

    .hero-slide {
        @apply opacity-0;
    }

    .hero-slide .btn-primary,
    .hero-slide h1,
    .hero-slide p {
        @apply transition-all duration-500 opacity-0;
        transform: rotateX(90deg);
    }

    .hero-slide img {
        @apply transition-all duration-500 origin-bottom scale-0 opacity-0;
    }

    .hero-slide.swiper-slide-active .btn-primary,
    .hero-slide.swiper-slide-active h1,
    .hero-slide.swiper-slide-active p {
        @apply opacity-100;
        transform: rotateX(0deg);
    }

    .hero-slide.swiper-slide-active img {
        @apply scale-100 opacity-100;
    }
}

.nav-menu-active {
    @apply visible max-h-[600px] scale-y-100 opacity-100;
}

.scroll-pink::-webkit-scrollbar-thumb {
    background-color: #fe1ca6;
    border-radius: 9999px;
}

.scroll-pink::-webkit-scrollbar {
    width: 15px;
}

.viewText {
    @apply invisible transition-all duration-300 origin-top scale-y-0 opacity-0 max-h-0 sm:visible sm:opacity-100 sm:max-h-max sm:scale-y-100;
}

.viewTextActive {
    @apply visible opacity-100 max-h-[2000px] scale-y-100 pb-7;
}

.text-overlay {
    @apply absolute bottom-4 left-0 right-0 z-20 h-20 block w-full max-w-[530px] blur-[7px] sm:hidden;
}

.text-overlayClosed {
    @apply invisible opacity-0 max-h-0;
}

.card-container {
    @apply w-full md:max-h-[1500px] md:overflow-hidden;
}

.card-container-active {
    @apply !h-auto !max-h-none !overflow-visible;
}

.card-overlay {
    @apply absolute bottom-0 left-0 right-0 hidden w-full h-44 bg-box md:block;
}

.cardoverlayClose {
    @apply invisible h-0 opacity-0;
}

.shadowTabActive {
    @apply !text-white !bg-dark-purple;
}

.shadowTabActive span {
    @apply !bg-white !text-dark-purple;
}

.tabcontent {
    @apply hidden;
}

.animation {
    animation: fadeEffect 1s;
}

@keyframes fadeEffect {
    from {
        opacity: 0;
    }

    to {
        opacity: 1;
    }
}

/*-- ::::::::::::wordpress solution:::::::::::: */
.wordpress-solution .active {
    @apply bg-light-purple;
}

.wordpress-solution .active p {
    @apply text-white;
}

.wordpress-solution-content ul {
    @apply pl-4 text-left list-disc;
}

.wordpress-solution-content ul li {
    @apply text-xs font-normal leading-6 text-light-purple md:text-base md:leading-8;
}

.referenceCategory .swiper-slide {
    width: auto !important;
}

#input-search {
    @apply relative h-10 w-full rounded-[10px] border border-[#EAEAEA] sm:max-w-[400px];
}

.page-title,
.entry-title {
    @apply mx-auto mb-6 text-3xl font-extrabold max-w-content text-neutral-900;
}

.page-content>*,
.entry-content>* {
    /* Content width from the `theme.json` file */
    @apply mx-auto max-w-content;
}

.entry-content>.alignwide {
    /* Wide width from the `theme.json` file */
    @apply max-w-wide;
}

.entry-content>.alignfull {
    @apply max-w-none;
}

.entry-content>.alignleft {
    @apply float-left mr-8;
}

.entry-content>.alignright {
    @apply float-right ml-8;
}

/*-- :::::::::::: blog navigation :::::::::::: */

.uw-blog-index-navigation a.page-numbers {
    @apply px-1;
}

.uw-blog-index-navigation span.page-numbers.current {
    @apply  text-theme-pink group-open:text-theme-pink;
}
