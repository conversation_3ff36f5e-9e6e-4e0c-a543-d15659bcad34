# Umíme Weby - Custom WordPress Theme

Custom WordPress theme pro firemní web Umíme Weby, založený na **[_tw (underscore tw)](https://underscoretw.com/)** foundation s moderní architekturou a Tailwind CSS.

## 🌍 Multi-jazyková architektura

Tento theme je sdílen mezi dvěma samostatnými WordPress instalacemi s vlastními databázemi a hostingem:

### Česká verze - www.atwel.cz
- **Produkce**: `master` větev
- **Demo**: `demo` větev
- Vlastní databáze s českým obsahem
- Samostatný hosting

### Anglická verze - www.atwel.com
- **Produkce**: `en-master` větev
- **Demo**: `en-demo` větev
- Vlastní databáze s anglickým obsahem
- Samostatný hosting

**Důležité**: Obě verze používají identickou funkcionalitu a konfiguraci, liš<PERSON> se pouze obsahem databáze.

## 🏗️ Architektura

Tento theme je postaven na **_tw (underscore tw)** foundation a využívá:

- **Namespace**: `Umimeweby\UWTheme\` pro všechny custom třídy
- **PSR-4 Autoloading** přes Composer
- **OOP přístup** pro všechny custom funkcionality
- **WordPress Coding Standards (WPCS)**
- **Moderní PHP 8.1+** s type hints a strict typing

### Struktura namespace

```
Umimeweby\UWTheme\
├── AdminDashboard\     # Správa admin dashboardu
├── AdminTables\        # Custom sloupce v admin tabulkách
├── CustomFields\       # Meta Box custom fields
├── CustomPostType\     # Custom post types
├── Form\              # Formuláře a jejich zpracování
├── Service\           # Služby a utility třídy
├── Settings\          # WordPress nastavení
├── ShortCodes\        # Custom shortcodes
└── Taxonomies\        # Custom taxonomie
```

## 🛠️ Tech Stack

### Backend
- **WordPress** 6.0+
- **PHP** 8.1+
- **_tw foundation** - [underscoretw.com](https://underscoretw.com/)
- **Composer** pro dependency management

### Frontend
- **Tailwind CSS** - utility-first CSS framework
- **FlowBite** - UI komponenty
- **Vanilla JavaScript** / jQuery
- **PostCSS** pro build proces

### Development Tools
- **PHPStan** - statická analýza kódu
- **PHP_CodeSniffer** - kontrola coding standards
- **WordPress Coding Standards (WPCS)**
- **npm** pro frontend build

## 📦 Composer Balíčky

### Production Dependencies
- `getresponse/sdk-php: ^3.0` - GetResponse API integrace

### Development Dependencies
- `phpstan/phpstan: ^1.10` - statická analýza
- `wp-coding-standards/wpcs: ^2.1.1` - WordPress coding standards
- `phpcompatibility/phpcompatibility-wp` - PHP kompatibilita
- `wp-cli/i18n-command` - internacionalizace

## 🚀 Instalace

### Předpoklady
- **WordPress** 6.0+
- **PHP** 8.1+
- **Node.js** 19+
- **Composer** 2.0+

### Požadované pluginy
1. **Meta Box** - https://cs.wordpress.org/plugins/meta-box/
2. **Meta Box AIO** - https://metabox.io/ (licencovaný plugin)
3. **WPS Hide Login** - https://cs.wordpress.org/plugins/wps-hide-login/
4. **WP Mail SMTP** - https://cs.wordpress.org/plugins/wp-mail-smtp/

### Kroky instalace

1. **Stažení a instalace WordPress** (verze 6.0+)

2. **Instalace požadovaných pluginů** (viz seznam výše)

3. **Naklonování theme**
   ```bash
   cd wp-content/themes/
   git clone [repository-url] umimeweby-cosmic-theme
   cd umimeweby-cosmic-theme
   ```

4. **Instalace dependencies** (pomocí Makefile)
   ```bash
   # Instalace všech dependencies (backend + frontend)
   make init
   
   # Nebo jednotlivě:
   make init-be    # Composer dependencies
   make init-fe    # NPM dependencies
   
   # První build
   npm run dev
   ```

5. **Aktivace theme** v WordPress administraci

### Příprava lokálního vývojového prostředí

**DŮLEŽITÉ**: Pro vývoj si vždy vytvořte samostatný lokální WordPress!

#### Kroky pro nastavení lokálního prostředí:

1. **Určete cílovou jazykovou verzi** pro kterou budete vyvíjet
2. **Stáhněte produkční databázi** odpovídající verze:
   - Pro CZ verzi: stáhněte databázi z www.atwel.cz
   - Pro EN verzi: stáhněte databázi z www.atwel.com
3. **Vytvořte lokální WordPress** s odpovídající databází
4. **Naklonujte správnou větev** podle cílové verze:
   ```bash
   # Pro českou verzi
   git checkout master    # pro produkční vývoj
   git checkout demo      # pro demo vývoj
   
   # Pro anglickou verzi
   git checkout en-master # pro produkční vývoj
   git checkout en-demo   # pro demo vývoj
   ```

#### Doporučený workflow:
- Vždy začněte s produkční databází odpovídající jazykové verze
- Testujte změny na lokálním prostředí před commitem
- Používejte správnou větev podle cílové verze

## 🔧 Vývoj

### Development workflow

```bash
# Spuštění watch módu pro Tailwind CSS
npm run watch

# Build pro produkci
npm run bundle

# Kontrola kódu pomocí PHPStan
composer phpstan
# nebo pomocí Makefile
make pre-commit

# Regenerace PHPStan baseline
composer regenerate-baseline
# nebo pomocí Makefile
make regenerate-baseline
```

### Makefile příkazy

Projekt obsahuje Makefile pro zjednodušení běžných úkolů:

```bash
# Inicializace projektu (backend + frontend)
make init

# Inicializace pouze backend dependencies
make init-be

# Inicializace pouze frontend dependencies  
make init-fe

# Pre-commit kontrola (PHPStan analýza)
make pre-commit

# Regenerace PHPStan baseline
make regenerate-baseline
```

### Coding Standards

- Dodržuj **WordPress Coding Standards (WPCS)**
- Všechny custom třídy v namespace `Umimeweby\UWTheme\`
- Používej **PSR-4 autoloading**
- **OOP přístup** pro všechny custom funkcionality
- **Type hints** a strict typing pro PHP 8.1+
- Escapování všech výstupů (`esc_html`, `esc_attr`, atd.)
- Používej **WordPress API** místo přímých SQL dotazů

### Přidávání nových funkcionalit

1. Vytvoř novou třídu v odpovídajícím namespace
2. Zaregistruj třídu v `functions.php`
3. Dodržuj WordPress hooks pattern
4. Přidej PHPDoc komentáře
5. Otestuj pomocí `composer phpstan`

## 🎨 Tailwind CSS

Theme využívá **Tailwind CSS** s custom konfigurací:

- Konfigurace: `tailwind/tailwind.config.js`
- Custom styly: `tailwind/custom/`
- Typography plugin: `tailwind/tailwind-typography.config.js`

Přidávej [Tailwind utility classes](https://tailwindcss.com/docs/utility-first) podle potřeby.

## 📧 GetResponse Integrace

Theme obsahuje integraci s **GetResponse** pro newsletter:

- API integrace přes `getresponse/sdk-php`
- Nastavení v WordPress admin: **UW nastavení/GetResponse**
- Test list ID: `ZBclb` (pouze pro testování)

## 📄 Page Templates

Dostupné page templates:

- `page-templates/page-with-hero.php` - stránka s HERO sekcí
- `page-templates/landing-with-top-menu.php` - landing page s top menu

## 🔧 Shortcodes

Přehled všech shortcodů je dostupný v **WordPress Admin Dashboard** jako widget.

### Dostupné shortcodes

- **[uw-references-filtered](docs/shortcodes/uw-references-filtered.md)** - univerzální filtrování referencí (podle ID nebo tagu)
- **uw-references-by-category** - zobrazení referencí s filtrem kategorií
- **uw-carousel-references** - karusel referencí
- **uw-client-logos** - zobrazení log klientů
- **uw-mini-case-studies** - mini case studies
- **uw-technologies** - zobrazení technologií
- **uw-text-testimonials** - textové testimonials
- **uw-why-select-us** - proč si vybrat nás
- **uw-new-or-existing-projects** - nové nebo existující projekty
- **[cta-nl-card](docs/shortcodes/cta-nl-card.md)** - CTA newsletter karta s konfigurovatelným nadpisem a popiskem

## 🚀 Deployment

### Deployment strategie pro multi-jazykové prostředí

#### Příprava před deploymentem:
```bash
# Build pro produkci
npm run bundle

# Kontrola kódu
make pre-commit
```

#### Deployment do produkce:

**Česká verze (www.atwel.cz):**
- Merge do `master` větve
- Automatický deployment na produkční server
- Testování na produkčním prostředí

**Anglická verze (www.atwel.com):**
- Merge do `en-master` větve  
- Automatický deployment na produkční server
- Testování na produkčním prostředí

#### Deployment do demo prostředí:

**Česká demo verze:**
- Merge do `demo` větve
- Deployment na demo server

**Anglická demo verze:**
- Merge do `en-demo` větve
- Deployment na demo server

#### Checklist před nasazením:
- [ ] Kód prošel PHPStan kontrolou (`make pre-commit`)
- [ ] Frontend assets jsou buildnuté (`npm run bundle`)
- [ ] Změny byly otestovány na lokálním prostředí s odpovídající databází
- [ ] Pull Request byl schválen
- [ ] Backup databáze byl vytvořen (pro produkční nasazení)

#### Rollback procedura:
V případě problémů po nasazení:
1. Okamžitě revertujte problematický commit
2. Proveďte nový deployment s revertovanou verzí
3. Obnovte databázi z backupu (pokud je potřeba)
4. Informujte tým o problému

#### Alternativní deployment (manuální):
```bash
# Build a vytvoření ZIP
npm run bundle
# Výsledný ZIP soubor nahraj přes "Upload Theme" v WordPress administraci
```

## 📝 Git Workflow

### Branch strategie pro multi-jazykové prostředí

#### Hlavní větve:
- **`master`** - produkce české verze (www.atwel.cz)
- **`demo`** - demo české verze
- **`en-master`** - produkce anglické verze (www.atwel.com)
- **`en-demo`** - demo anglické verze

#### Workflow pro vývoj:

**Pro změny ovlivňující obě jazykové verze:**
1. Vytvořte feature branch z příslušné větve
2. Implementujte změny
3. Vytvořte **2 samostatné Pull Requesty**:
   - Jeden do CZ větve (`master` nebo `demo`)
   - Jeden do EN větve (`en-master` nebo `en-demo`)

**Pro změny specifické pro jednu jazykovou verzi:**
1. Vytvořte feature branch z příslušné cílové větve
2. Implementujte změny
3. Vytvořte Pull Request do odpovídající větve

#### Conventional Commits (commit messages):

```bash
feat: přidání nové funkcionality
fix: oprava chyby
docs: aktualizace dokumentace
style: formátování kódu
refactor: refaktoring bez změny funkcionality
```

#### Názvy feature branches:
Používejte formát: `[číslo-úkolu]-[popis]`

Příklady:
```bash
37099366-new-contact-form
37099367-fix-mobile-menu
37099368-update-homepage-carousel
```

#### Důležité zásady:
- **Nikdy nevyvíjejte přímo na produkčních větvích** (`master`, `en-master`)
- Vždy testujte změny na lokálním prostředí s odpovídající databází
- Pro změny ovlivňující obě verze vytvářejte vždy 2 PR
- Vždy používejte číslo úkolu v názvu feature branch

## ⚠️ Důležité upozornění pro vývojáře

### Zásady pro bezpečný vývoj:

#### ❌ NIKDY:
- Nevyvíjejte přímo na produkčních větvích (`master`, `en-master`)
- Necommitujte změny bez předchozího testování na lokálním prostředí
- Nezapomeňte vytvořit 2 PR pro změny ovlivňující obě jazykové verze
- Nepoužívejte nesprávnou databázi pro testování (CZ databázi pro EN verzi a naopak)

#### ✅ VŽDY:
- Vytvořte si samostatný lokální WordPress pro vývoj
- Použijte správnou databázi odpovídající cílové jazykové verzi
- Otestujte změny na lokálním prostředí před commitem
- Zkontrolujte, že pracujete na správné větvi
- Spusťte `make pre-commit` před každým commitem
- Vytvořte popisný název pro feature branch

#### 🔍 Kontrola před commitem:
```bash
# Zkontrolujte aktuální větev
git branch

# Zkontrolujte stav změn
git status

# Spusťte kontrolu kódu
make pre-commit

# Build frontend assets
npm run bundle
```

#### 🚨 V případě chyby:
- Okamžitě informujte tým
- Nepokračujte v práci dokud není problém vyřešen
- Použijte rollback proceduru z deployment sekce

## 👥 Autoři

- **Vít Šafařík** - Vedoucí projektu a programátor
- **Richard Koza** -  programátor, CEO

## 📚 Dokumentace

### Obecná dokumentace
- **_tw Documentation**: https://underscoretw.com/docs/
- **Tailwind CSS**: https://tailwindcss.com/docs/
- **WordPress Codex**: https://codex.wordpress.org/
- **Meta Box**: https://docs.metabox.io/

### Projektová dokumentace
- **[UW Service URL Router](UW_Service_URL_Router_README.md)** - Dokumentace pro custom URL routing systém

## 🆘 Podpora

Pro technické dotazy a podporu kontaktuj vývojový tým.

---

*Tento theme je založen na [_tw (underscore tw)](https://underscoretw.com/) foundation a využívá moderní WordPress development praktiky.*
